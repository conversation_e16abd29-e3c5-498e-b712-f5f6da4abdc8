
import { Product, Category, StockMovement, LowStockAlert } from '../types/inventory';
import { supabase } from '../integrations/supabase/client';
import { Database } from '../integrations/supabase/types';
import { realTimeService, syncProductData, syncInventoryData, syncImageData } from './realTimeService';

// Type aliases for better readability
type ProductRow = Database['public']['Tables']['products']['Row'];
type ProductInsert = Database['public']['Tables']['products']['Insert'];
type ProductUpdate = Database['public']['Tables']['products']['Update'];
type CategoryRow = Database['public']['Tables']['categories']['Row'];
type CategoryInsert = Database['public']['Tables']['categories']['Insert'];
type CategoryUpdate = Database['public']['Tables']['categories']['Update'];
type StockMovementRow = Database['public']['Tables']['stock_movements']['Row'];
type StockMovementInsert = Database['public']['Tables']['stock_movements']['Insert'];
type BranchInventoryRow = Database['public']['Tables']['branch_inventory']['Row'];
type BranchInventoryInsert = Database['public']['Tables']['branch_inventory']['Insert'];
type BranchInventoryUpdate = Database['public']['Tables']['branch_inventory']['Update'];

// Event system for real-time updates
type InventoryEventType = 'product-added' | 'product-updated' | 'product-deleted' | 'stock-updated';
type InventoryEventListener = (eventType: InventoryEventType, data: any) => void;

class InventoryEventManager {
  private listeners: InventoryEventListener[] = [];

  subscribe(listener: InventoryEventListener) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  emit(eventType: InventoryEventType, data: any) {
    this.listeners.forEach(listener => listener(eventType, data));
  }
}

export const inventoryEventManager = new InventoryEventManager();

// Helper functions to convert database rows to our types
const convertToProduct = (productRow: ProductRow, categoryName?: string): Product => {
  return {
    id: productRow.id,
    title: productRow.title,
    description: productRow.description || '',
    category: categoryName || 'Uncategorized',
    brand: productRow.brand || '',
    price: Number(productRow.price),
    resellerPrice: Number(productRow.reseller_price || productRow.price),
    image: productRow.featured_image || '/placeholder.svg',
    featuredImage: productRow.featured_image || '/placeholder.svg',
    thumbnailImages: productRow.thumbnail_images || ['/placeholder.svg'],
    rating: Number(productRow.rating || 0),
    stock: productRow.stock,
    minStock: productRow.min_stock,
    isActive: productRow.is_active,
    isNew: productRow.is_new,
    sku: productRow.sku,
    weight: productRow.weight ? Number(productRow.weight) : undefined,
    dimensions: productRow.dimensions as Product['dimensions'],
    tags: productRow.tags || [],
    createdAt: productRow.created_at,
    updatedAt: productRow.updated_at,
  };
};

const convertToCategory = (categoryRow: CategoryRow): Category => {
  return {
    id: categoryRow.id,
    name: categoryRow.name,
    description: categoryRow.description || undefined,
    isActive: categoryRow.is_active,
    parentId: categoryRow.parent_id || undefined,
    createdAt: categoryRow.created_at,
    updatedAt: categoryRow.updated_at,
  };
};

const convertToStockMovement = (movementRow: StockMovementRow): StockMovement => {
  return {
    id: movementRow.id,
    productId: movementRow.product_id,
    type: movementRow.movement_type as StockMovement['type'],
    quantity: movementRow.quantity,
    reason: movementRow.reason || '',
    reference: movementRow.reference_id || undefined,
    createdBy: movementRow.created_by || 'system',
    createdAt: movementRow.created_at,
  };
};

// Enhanced Product Management with database integration
export const getProducts = async (filters?: {
  category?: string;
  isActive?: boolean;
  lowStock?: boolean;
  branchId?: string;
}): Promise<Product[]> => {
  try {
    let query = supabase
      .from('products')
      .select(`
        *,
        categories (
          name
        )
      `)
      .order('created_at', { ascending: false });

    // Apply filters
    if (filters?.category) {
      query = query.eq('categories.name', filters.category);
    }

    if (filters?.isActive !== undefined) {
      query = query.eq('is_active', filters.isActive);
    }

    const { data: products, error } = await query;

    if (error) {
      console.error('Error fetching products:', error);
      throw error;
    }

    let filteredProducts = products?.map(product =>
      convertToProduct(product, product.categories?.name)
    ) || [];

    // Apply low stock filter after conversion
    if (filters?.lowStock) {
      filteredProducts = filteredProducts.filter(p => p.stock <= p.minStock);
    }

    return filteredProducts;
  } catch (error) {
    console.error('Error in getProducts:', error);
    return [];
  }
};
export const getProductById = async (id: string): Promise<Product | null> => {
  try {
    const { data: product, error } = await supabase
      .from('products')
      .select(`
        *,
        categories (
          name
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching product:', error);
      return null;
    }

    return product ? convertToProduct(product, product.categories?.name) : null;
  } catch (error) {
    console.error('Error in getProductById:', error);
    return null;
  }
};

export const createProduct = async (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<Product | null> => {
  try {
    // First, get or create category
    let categoryId: string | null = null;
    if (productData.category) {
      const { data: category } = await supabase
        .from('categories')
        .select('id')
        .eq('name', productData.category)
        .single();

      categoryId = category?.id || null;
    }

    const productInsert: ProductInsert = {
      title: productData.title,
      description: productData.description,
      sku: productData.sku,
      category_id: categoryId,
      brand: productData.brand,
      price: productData.price,
      reseller_price: productData.resellerPrice,
      featured_image: productData.featuredImage,
      thumbnail_images: productData.thumbnailImages,
      rating: productData.rating,
      stock: productData.stock,
      min_stock: productData.minStock,
      weight: productData.weight,
      dimensions: productData.dimensions,
      tags: productData.tags,
      is_active: productData.isActive,
      is_new: productData.isNew,
    };

    const { data: newProduct, error } = await supabase
      .from('products')
      .insert(productInsert)
      .select(`
        *,
        categories (
          name
        )
      `)
      .single();

    if (error) {
      console.error('Error creating product:', error);
      throw error;
    }

    if (newProduct) {
      const product = convertToProduct(newProduct, newProduct.categories?.name);

      // Emit real-time event
      inventoryEventManager.emit('product-added', product);

      return product;
    }

    return null;
  } catch (error) {
    console.error('Error in createProduct:', error);
    return null;
  }
};
// Category Management
export const getCategories = async (): Promise<Category[]> => {
  try {
    const { data: categories, error } = await supabase
      .from('categories')
      .select('*')
      .eq('is_active', true)
      .order('sort_order', { ascending: true });

    if (error) {
      console.error('Error fetching categories:', error);
      throw error;
    }

    return categories?.map(convertToCategory) || [];
  } catch (error) {
    console.error('Error in getCategories:', error);
    return [];
  }
};

export const createCategory = async (categoryData: Omit<Category, 'id' | 'createdAt' | 'updatedAt'>): Promise<Category | null> => {
  try {
    const categoryInsert: CategoryInsert = {
      name: categoryData.name,
      description: categoryData.description,
      parent_id: categoryData.parentId,
      is_active: categoryData.isActive,
    };

    const { data: newCategory, error } = await supabase
      .from('categories')
      .insert(categoryInsert)
      .select()
      .single();

    if (error) {
      console.error('Error creating category:', error);
      throw error;
    }

    return newCategory ? convertToCategory(newCategory) : null;
  } catch (error) {
    console.error('Error in createCategory:', error);
    return null;
  }
};

export const updateProduct = async (id: string, updates: Partial<Product>, userId?: string): Promise<Product | null> => {
  try {
    // Get category ID if category name is provided
    let categoryId: string | null = null;
    if (updates.category) {
      const { data: category } = await supabase
        .from('categories')
        .select('id')
        .eq('name', updates.category)
        .single();

      categoryId = category?.id || null;
    }

    const productUpdate: ProductUpdate = {
      title: updates.title,
      description: updates.description,
      sku: updates.sku,
      category_id: categoryId,
      brand: updates.brand,
      price: updates.price,
      reseller_price: updates.resellerPrice,
      featured_image: updates.featuredImage,
      thumbnail_images: updates.thumbnailImages,
      rating: updates.rating,
      stock: updates.stock,
      min_stock: updates.minStock,
      weight: updates.weight,
      dimensions: updates.dimensions,
      tags: updates.tags,
      is_active: updates.isActive,
      is_new: updates.isNew,
    };

    const { data: updatedProduct, error } = await supabase
      .from('products')
      .update(productUpdate)
      .eq('id', id)
      .select(`
        *,
        categories (
          name
        )
      `)
      .single();

    if (error) {
      console.error('Error updating product:', error);
      throw error;
    }

    if (updatedProduct) {
      const product = convertToProduct(updatedProduct, updatedProduct.categories?.name);

      // Emit real-time event
      inventoryEventManager.emit('product-updated', product);

      // Sync with real-time service for cross-user synchronization
      syncProductData(id, product, userId);

      // If images were updated, sync image data specifically
      if (updates.featuredImage || updates.thumbnailImages) {
        syncImageData(id, {
          featuredImage: product.featuredImage,
          thumbnailImages: product.thumbnailImages
        }, userId);
      }

      return product;
    }

    return null;
  } catch (error) {
    console.error('Error in updateProduct:', error);
    return null;
  }
};
};

export const deleteProduct = async (id: string): Promise<boolean> => {
  try {
    // Soft delete by setting is_active to false
    const { error } = await supabase
      .from('products')
      .update({ is_active: false })
      .eq('id', id);

    if (error) {
      console.error('Error deleting product:', error);
      throw error;
    }

    // Emit real-time event
    inventoryEventManager.emit('product-deleted', { id });

    return true;
  } catch (error) {
    console.error('Error in deleteProduct:', error);
    return false;
  }
};

// Enhanced Stock Management with database integration
export const updateStock = async (
  productId: string,
  quantity: number,
  type: 'in' | 'out' | 'adjustment',
  reason: string,
  reference?: string,
  createdBy: string = 'system',
  branchId?: string
): Promise<boolean> => {
  try {
    // Create stock movement record
    const movementInsert: StockMovementInsert = {
      product_id: productId,
      branch_id: branchId,
      movement_type: type,
      quantity,
      reason,
      reference_id: reference,
      reference_type: reference ? 'order' : undefined,
      created_by: createdBy,
    };

    const { data: movement, error: movementError } = await supabase
      .from('stock_movements')
      .insert(movementInsert)
      .select()
      .single();

    if (movementError) {
      console.error('Error creating stock movement:', movementError);
      throw movementError;
    }

    // The database trigger will automatically update the product stock
    // But we still need to emit the real-time event
    const product = await getProductById(productId);
    if (product && movement) {
      inventoryEventManager.emit('stock-updated', {
        productId,
        oldStock: product.stock,
        newStock: product.stock, // This would be updated by the trigger
        movement: convertToStockMovement(movement)
      });
    }

    return true;
  } catch (error) {
    console.error('Error in updateStock:', error);
    return false;
  }
};
export const getStockMovements = async (productId?: string): Promise<StockMovement[]> => {
  try {
    let query = supabase
      .from('stock_movements')
      .select('*')
      .order('created_at', { ascending: false });

    if (productId) {
      query = query.eq('product_id', productId);
    }

    const { data: movements, error } = await query;

    if (error) {
      console.error('Error fetching stock movements:', error);
      throw error;
    }

    return movements?.map(convertToStockMovement) || [];
  } catch (error) {
    console.error('Error in getStockMovements:', error);
    return [];
  }
};

// Low Stock Alerts
export const getLowStockAlerts = async (): Promise<LowStockAlert[]> => {
  try {
    const { data: alerts, error } = await supabase
      .from('low_stock_alerts')
      .select(`
        *,
        products (
          title,
          categories (
            name
          )
        )
      `)
      .eq('is_resolved', false)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching low stock alerts:', error);
      throw error;
    }

    return alerts?.map(alert => ({
      id: alert.id,
      productId: alert.product_id,
      productTitle: alert.products?.title || 'Unknown Product',
      currentStock: alert.current_stock,
      minStock: alert.min_stock,
      category: alert.products?.categories?.name || 'Uncategorized',
      isResolved: alert.is_resolved,
      createdAt: alert.created_at,
    })) || [];
  } catch (error) {
    console.error('Error in getLowStockAlerts:', error);
    return [];
  }
};

export const updateCategory = async (id: string, updates: Partial<Category>): Promise<Category | null> => {
  try {
    const categoryUpdate: CategoryUpdate = {
      name: updates.name,
      description: updates.description,
      parent_id: updates.parentId,
      is_active: updates.isActive,
    };

    const { data: updatedCategory, error } = await supabase
      .from('categories')
      .update(categoryUpdate)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating category:', error);
      throw error;
    }

    return updatedCategory ? convertToCategory(updatedCategory) : null;
  } catch (error) {
    console.error('Error in updateCategory:', error);
    return null;
  }
};

// Bulk operations for better performance
export const bulkUpdateProducts = async (updates: Array<{ id: string, data: Partial<Product> }>): Promise<Product[]> => {
  const updatedProducts: Product[] = [];

  for (const update of updates) {
    const product = await updateProduct(update.id, update.data);
    if (product) {
      updatedProducts.push(product);
    }
  }

  return updatedProducts;
};
