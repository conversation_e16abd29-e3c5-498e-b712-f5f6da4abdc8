import { realTimeService } from './realTimeService';
import { supabase } from '../integrations/supabase/client';
import { Database } from '../integrations/supabase/types';
import { User as UserType } from '../types/user';

// Type aliases for better readability
type UserRow = Database['public']['Tables']['users']['Row'];
type UserInsert = Database['public']['Tables']['users']['Insert'];
type UserUpdate = Database['public']['Tables']['users']['Update'];
type UserProfileRow = Database['public']['Tables']['user_profiles']['Row'];
type UserProfileInsert = Database['public']['Tables']['user_profiles']['Insert'];
type UserProfileUpdate = Database['public']['Tables']['user_profiles']['Update'];

export interface User {
  id: string;
  email: string;
  fullName: string;
  userType: 'admin' | 'manager' | 'client' | 'reseller' | 'delivery_person';
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  permissions: string[];
  avatar?: string;
  phone?: string;
  department?: string;
  branch?: string;
  city?: string;
  isCompany?: boolean;
  companyName?: string;
  iceNumber?: string;
}

export interface CreateUserData {
  email: string;
  fullName: string;
  userType: 'admin' | 'manager' | 'client' | 'reseller' | 'delivery_person';
  password?: string;
  phone?: string;
  department?: string;
  branch?: string;
  permissions?: string[];
  city?: string;
  isCompany?: boolean;
  companyName?: string;
  iceNumber?: string;
  companyAddress?: string;
  companyPhone?: string;
  companyCity?: string;
  companyEmail?: string;
  taxId?: string;
  legalForm?: string;
}

// Helper function to convert database row to User type
const convertToUser = (userRow: UserRow, profileRow?: UserProfileRow): User => {
  return {
    id: userRow.id,
    email: userRow.email,
    fullName: userRow.full_name,
    userType: userRow.user_type as User['userType'],
    phone: userRow.phone || undefined,
    city: userRow.city || undefined,
    isActive: userRow.is_active,
    createdAt: userRow.created_at,
    updatedAt: userRow.updated_at,
    lastLogin: userRow.last_login || undefined,
    isCompany: userRow.is_company || false,
    companyName: userRow.company_name || undefined,
    iceNumber: userRow.ice_number || undefined,
    avatar: profileRow?.avatar_url || undefined,
    department: profileRow?.job_title || undefined,
    permissions: [], // This would come from a separate permissions system
    createdBy: 'system', // Default value, would be tracked separately
    branch: undefined, // Would be determined by user's assigned branch
  };
};

// User management functions using Supabase
export const getUsers = async (): Promise<User[]> => {
  try {
    const { data: users, error } = await supabase
      .from('users')
      .select(`
        *,
        user_profiles (
          avatar_url,
          job_title
        )
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching users:', error);
      throw error;
    }

    return users?.map(user => convertToUser(user, user.user_profiles?.[0])) || [];
  } catch (error) {
    console.error('Error in getUsers:', error);
    return [];
  }
};

export const getUserById = async (id: string): Promise<User | null> => {
  try {
    const { data: user, error } = await supabase
      .from('users')
      .select(`
        *,
        user_profiles (
          avatar_url,
          job_title
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching user:', error);
      return null;
    }

    return user ? convertToUser(user, user.user_profiles?.[0]) : null;
  } catch (error) {
    console.error('Error in getUserById:', error);
    return null;
  }
};

export const createUser = async (userData: CreateUserData, createdBy: string): Promise<User | null> => {
  try {
    // Insert user into users table
    const userInsert: UserInsert = {
      email: userData.email,
      full_name: userData.fullName,
      user_type: userData.userType,
      phone: userData.phone,
      city: userData.city,
      is_company: userData.isCompany || false,
      company_name: userData.companyName,
      ice_number: userData.iceNumber,
      company_address: userData.companyAddress,
      company_phone: userData.companyPhone,
      company_city: userData.companyCity,
      company_email: userData.companyEmail,
      tax_id: userData.taxId,
      legal_form: userData.legalForm,
    };

    const { data: newUser, error: userError } = await supabase
      .from('users')
      .insert(userInsert)
      .select()
      .single();

    if (userError) {
      console.error('Error creating user:', userError);
      throw userError;
    }

    // Create user profile
    if (newUser) {
      const profileInsert: UserProfileInsert = {
        user_id: newUser.id,
        job_title: userData.department,
        language: 'en',
        currency: 'MAD',
        timezone: 'Africa/Casablanca',
      };

      const { error: profileError } = await supabase
        .from('user_profiles')
        .insert(profileInsert);

      if (profileError) {
        console.error('Error creating user profile:', profileError);
        // Don't throw here, user was created successfully
      }

      const user = convertToUser(newUser);

      // Emit real-time event
      realTimeService.emit('user-created', { user, createdBy });

      return user;
    }

    return null;
  } catch (error) {
    console.error('Error in createUser:', error);
    return null;
  }
};
export const updateUser = async (id: string, updates: Partial<CreateUserData>, updatedBy: string): Promise<User | null> => {
  try {
    // Update user in users table
    const userUpdate: UserUpdate = {
      full_name: updates.fullName,
      user_type: updates.userType,
      phone: updates.phone,
      city: updates.city,
      is_company: updates.isCompany,
      company_name: updates.companyName,
      ice_number: updates.iceNumber,
      company_address: updates.companyAddress,
      company_phone: updates.companyPhone,
      company_city: updates.companyCity,
      company_email: updates.companyEmail,
      tax_id: updates.taxId,
      legal_form: updates.legalForm,
    };

    const { data: updatedUser, error: userError } = await supabase
      .from('users')
      .update(userUpdate)
      .eq('id', id)
      .select()
      .single();

    if (userError) {
      console.error('Error updating user:', userError);
      throw userError;
    }

    // Update user profile if needed
    if (updates.department) {
      const profileUpdate: UserProfileUpdate = {
        job_title: updates.department,
      };

      const { error: profileError } = await supabase
        .from('user_profiles')
        .update(profileUpdate)
        .eq('user_id', id);

      if (profileError) {
        console.error('Error updating user profile:', profileError);
        // Don't throw here, user was updated successfully
      }
    }

    if (updatedUser) {
      const user = convertToUser(updatedUser);

      // Emit real-time event
      realTimeService.emit('user-updated', { user, updatedBy });

      return user;
    }

    return null;
  } catch (error) {
    console.error('Error in updateUser:', error);
    return null;
  }
};
};

export const getAdminUsers = async (): Promise<User[]> => {
  try {
    const { data: users, error } = await supabase
      .from('users')
      .select(`
        *,
        user_profiles (
          avatar_url,
          job_title
        )
      `)
      .eq('user_type', 'admin')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching admin users:', error);
      throw error;
    }

    return users?.map(user => convertToUser(user, user.user_profiles?.[0])) || [];
  } catch (error) {
    console.error('Error in getAdminUsers:', error);
    return [];
  }
};

export const getManagerUsers = async (): Promise<User[]> => {
  try {
    const { data: users, error } = await supabase
      .from('users')
      .select(`
        *,
        user_profiles (
          avatar_url,
          job_title
        )
      `)
      .eq('user_type', 'manager')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching manager users:', error);
      throw error;
    }

    return users?.map(user => convertToUser(user, user.user_profiles?.[0])) || [];
  } catch (error) {
    console.error('Error in getManagerUsers:', error);
    return [];
  }
};

export const deleteUser = async (id: string, deletedBy: string): Promise<boolean> => {
  try {
    // Soft delete by setting is_active to false
    const { error } = await supabase
      .from('users')
      .update({ is_active: false })
      .eq('id', id);

    if (error) {
      console.error('Error deleting user:', error);
      throw error;
    }

    // Emit real-time event
    realTimeService.emit('user-deleted', { userId: id, deletedBy });

    return true;
  } catch (error) {
    console.error('Error in deleteUser:', error);
    return false;
  }
};
export const toggleUserStatus = async (id: string, updatedBy: string): Promise<User | null> => {
  try {
    // First get the current user to check status
    const currentUser = await getUserById(id);
    if (!currentUser) return null;

    // Prevent deactivating the last admin
    if (currentUser.userType === 'admin' && currentUser.isActive) {
      const activeAdmins = await getAdminUsers();
      const activeAdminCount = activeAdmins.filter(u => u.isActive).length;
      if (activeAdminCount <= 1) {
        throw new Error('Cannot deactivate the last active admin user');
      }
    }

    const newStatus = !currentUser.isActive;

    const { data: updatedUser, error } = await supabase
      .from('users')
      .update({ is_active: newStatus })
      .eq('id', id)
      .select(`
        *,
        user_profiles (
          avatar_url,
          job_title
        )
      `)
      .single();

    if (error) {
      console.error('Error toggling user status:', error);
      throw error;
    }

    if (updatedUser) {
      const user = convertToUser(updatedUser, updatedUser.user_profiles?.[0]);

      // Emit real-time event
      realTimeService.emit('user-status-changed', {
        userId: id,
        isActive: newStatus,
        updatedBy
      });

      return user;
    }

    return null;
  } catch (error) {
    console.error('Error in toggleUserStatus:', error);
    return null;
  }
};
  return user;
};

export const searchUsers = async (query: string): Promise<User[]> => {
  try {
    const searchTerm = `%${query.toLowerCase()}%`;

    const { data: users, error } = await supabase
      .from('users')
      .select(`
        *,
        user_profiles (
          avatar_url,
          job_title
        )
      `)
      .or(`full_name.ilike.${searchTerm},email.ilike.${searchTerm},company_name.ilike.${searchTerm}`)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error searching users:', error);
      throw error;
    }

    return users?.map(user => convertToUser(user, user.user_profiles?.[0])) || [];
  } catch (error) {
    console.error('Error in searchUsers:', error);
    return [];
  }
};

export const getUserStats = async (): Promise<{
  total: number;
  admins: number;
  managers: number;
  clients: number;
  resellers: number;
  active: number;
  inactive: number;
  recentlyCreated: number;
}> => {
  try {
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);

    // Get total count
    const { count: total, error: totalError } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true });

    // Get counts by user type
    const { data: userTypes, error: typesError } = await supabase
      .from('users')
      .select('user_type')
      .not('user_type', 'is', null);

    // Get active/inactive counts
    const { count: active, error: activeError } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true);

    // Get recently created count
    const { count: recentlyCreated, error: recentError } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', weekAgo.toISOString());

    if (totalError || typesError || activeError || recentError) {
      console.error('Error getting user stats:', { totalError, typesError, activeError, recentError });
      throw new Error('Failed to get user statistics');
    }

    // Count user types
    const typeCounts = userTypes?.reduce((acc, user) => {
      acc[user.user_type] = (acc[user.user_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    return {
      total: total || 0,
      admins: typeCounts.admin || 0,
      managers: typeCounts.manager || 0,
      clients: typeCounts.client || 0,
      resellers: typeCounts.reseller || 0,
      active: active || 0,
      inactive: (total || 0) - (active || 0),
      recentlyCreated: recentlyCreated || 0
    };
  } catch (error) {
    console.error('Error in getUserStats:', error);
    return {
      total: 0,
      admins: 0,
      managers: 0,
      clients: 0,
      resellers: 0,
      active: 0,
      inactive: 0,
      recentlyCreated: 0
    };
  }
};

export default {
  getUsers,
  getUserById,
  getAdminUsers,
  getManagerUsers,
  createUser,
  updateUser,
  deleteUser,
  toggleUserStatus,
  searchUsers,
  getUserStats
};
